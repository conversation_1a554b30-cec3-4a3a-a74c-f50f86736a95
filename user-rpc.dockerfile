FROM golang:alpine AS builder

LABEL stage=gobuilder

<PERSON><PERSON><PERSON> CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk update --no-cache && apk add --no-cache tzdata

WORKDIR /build

ADD go.mod .
ADD go.sum .
RUN go mod download
COPY . .
COPY application/user/rpc/etc/user-rpc-dev.yaml /app/etc/user-rpc.yaml
RUN go build -ldflags="-s -w" -o /app/user-rpc application/user/rpc/user.go

FROM scratch

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /app/user-rpc /app/user-rpc
COPY --from=builder /app/etc /app/etc

CMD ["./user-rpc", "-f", "etc/user-rpc.yaml"]
