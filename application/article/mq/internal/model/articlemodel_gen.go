// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	articleFieldNames          = builder.RawFieldNames(&Article{})
	articleRows                = strings.Join(articleFieldNames, ",")
	articleRowsExpectAutoSet   = strings.Join(stringx.Remove(articleFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	articleRowsWithPlaceHolder = strings.Join(stringx.Remove(articleFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	articleModel interface {
		Insert(ctx context.Context, data *Article) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Article, error)
		Update(ctx context.Context, data *Article) error
		Delete(ctx context.Context, id int64) error
	}

	defaultArticleModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Article struct {
		Id          int64     `db:"id"`           // 主键ID
		Title       string    `db:"title"`        // 标题
		Content     string    `db:"content"`      // 内容
		Cover       string    `db:"cover"`        // 封面
		Description string    `db:"description"`  // 描述
		AuthorId    int64     `db:"author_id"`    // 作者ID
		Status      int64     `db:"status"`       // 状态 0:待审核 1:审核不通过 2:可见
		CommentNum  int64     `db:"comment_num"`  // 评论数
		LikeNum     int64     `db:"like_num"`     // 点赞数
		CollectNum  int64     `db:"collect_num"`  // 收藏数
		ViewNum     int64     `db:"view_num"`     // 浏览数
		ShareNum    int64     `db:"share_num"`    // 分享数
		TagIds      string    `db:"tag_ids"`      // 标签ID
		PublishTime time.Time `db:"publish_time"` // 发布时间
		CreateTime  time.Time `db:"create_time"`  // 创建时间
		UpdateTime  time.Time `db:"update_time"`  // 最后修改时间
	}
)

func newArticleModel(conn sqlx.SqlConn) *defaultArticleModel {
	return &defaultArticleModel{
		conn:  conn,
		table: "`article`",
	}
}

func (m *defaultArticleModel) withSession(session sqlx.Session) *defaultArticleModel {
	return &defaultArticleModel{
		conn:  sqlx.NewSqlConnFromSession(session),
		table: "`article`",
	}
}

func (m *defaultArticleModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultArticleModel) FindOne(ctx context.Context, id int64) (*Article, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", articleRows, m.table)
	var resp Article
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultArticleModel) Insert(ctx context.Context, data *Article) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, articleRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Title, data.Content, data.Cover, data.Description, data.AuthorId, data.Status, data.CommentNum, data.LikeNum, data.CollectNum, data.ViewNum, data.ShareNum, data.TagIds, data.PublishTime)
	return ret, err
}

func (m *defaultArticleModel) Update(ctx context.Context, data *Article) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, articleRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Title, data.Content, data.Cover, data.Description, data.AuthorId, data.Status, data.CommentNum, data.LikeNum, data.CollectNum, data.ViewNum, data.ShareNum, data.TagIds, data.PublishTime, data.Id)
	return err
}

func (m *defaultArticleModel) tableName() string {
	return m.table
}
