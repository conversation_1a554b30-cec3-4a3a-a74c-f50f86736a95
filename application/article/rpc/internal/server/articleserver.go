// Code generated by goctl. DO NOT EDIT.
// Source: article.proto

package server

import (
	"context"

	"beyond/application/article/rpc/internal/logic"
	"beyond/application/article/rpc/internal/svc"
	"beyond/application/article/rpc/pb"
)

type ArticleServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedArticleServer
}

func NewArticleServer(svcCtx *svc.ServiceContext) *ArticleServer {
	return &ArticleServer{
		svcCtx: svcCtx,
	}
}

func (s *ArticleServer) Publish(ctx context.Context, in *pb.PublishRequest) (*pb.PublishResponse, error) {
	l := logic.NewPublishLogic(ctx, s.svcCtx)
	return l.Publish(in)
}

func (s *ArticleServer) Articles(ctx context.Context, in *pb.ArticlesRequest) (*pb.ArticlesResponse, error) {
	l := logic.NewArticlesLogic(ctx, s.svcCtx)
	return l.Articles(in)
}

func (s *ArticleServer) ArticleDelete(ctx context.Context, in *pb.ArticleDeleteRequest) (*pb.ArticleDeleteResponse, error) {
	l := logic.NewArticleDeleteLogic(ctx, s.svcCtx)
	return l.ArticleDelete(in)
}

func (s *ArticleServer) ArticleDetail(ctx context.Context, in *pb.ArticleDetailRequest) (*pb.ArticleDetailResponse, error) {
	l := logic.NewArticleDetailLogic(ctx, s.svcCtx)
	return l.ArticleDetail(in)
}
