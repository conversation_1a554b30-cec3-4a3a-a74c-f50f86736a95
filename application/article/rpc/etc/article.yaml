Name: article.rpc
ListenOn: 0.0.0.0:9090
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: article.rpc
DataSource: root:Zsg123456@tcp(127.0.0.1:3306)/beyond_article?parseTime=true&loc=Local
CacheRedis:
  - Host: 127.0.0.1:6379
    Pass:
    Type: node
BizRedis:
  Host: 127.0.0.1:6379
  Pass:
  Type: node
Consul:
  Host: 127.0.0.1:8500
  Key: article-rpc
  Meta:
    env: test
    service_group: beyond
    service_name: article-rpc
Prometheus:
  Host: 0.0.0.0
  Port: 9102
  Path: /metrics
