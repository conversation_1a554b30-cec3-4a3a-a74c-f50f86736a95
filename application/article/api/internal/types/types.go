// Code generated by goctl. DO NOT EDIT.
package types

type UploadCoverResponse struct {
	CoverUrl string `json:"cover_url"`
}

type PublishRequest struct {
	Title       string `json:"title"`
	Content     string `json:"content"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
}

type PublishResponse struct {
	ArticleId int64 `json:"article_id"`
}

type ArticleDetailRequest struct {
	ArticleId int64 `form:"article_id"`
}

type ArticleDetailResponse struct {
	Title       string `json:"title"`
	Content     string `json:"content"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	AuthorId    string `json:"author_id"`
	AuthorName  string `json:"author_name"`
}

type ArticleListRequest struct {
	AuthorId  int64 `form:"author_id"`
	Cursor    int64 `form:"cursor"`
	PageSize  int64 `form:"page_size"`
	SortType  int32 `form:"sort_type"`
	ArticleId int64 `form:"article_id"`
}

type ArticleInfo struct {
	ArticleId   int64  `json:"article_id"`
	Title       string `json:"title"`
	Content     string `json:"content"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
}

type ArticleListResponse struct {
	Articles []ArticleInfo `json:"articles"`
}
