syntax = "proto3";

package service;
option go_package="./pb";

service Follow {
  // 关注
  rpc Follow (FollowRequest) returns (FollowResponse);
  // 取消关注
  rpc UnFollow (UnFollowRequest) returns (UnFollowResponse);
  // 关注列表
  rpc FollowList (FollowListRequest) returns (FollowListResponse);
  // 粉丝列表
  rpc FansList (FansListRequest) returns (FansListResponse);
}

message FollowRequest {
  int64 userId = 1; // 关注者
  int64 followedUserId = 2; // 被关注者
}

message FollowResponse {
}

message UnFollowRequest {
  int64 userId = 1;
  int64 followedUserId = 2;
}

message UnFollowResponse {
}

message FollowListRequest {
  int64 Id = 1;
  int64 userId = 2;
  int64 cursor = 3;
  int64 pageSize = 4;
}

message FollowItem {
  int64 Id = 1;
  int64 followedUserId = 2; // 被关注者
  int64 fansCount = 3; // 粉丝数
  int64 createTime = 4; // 关注时间
}

message FollowListResponse {
  repeated FollowItem items = 1;
  int64 cursor = 2;
  bool isEnd = 3;
  int64 Id = 4;
}

message FansListRequest {
  int64 userId = 1;
  int64 cursor = 2;
  int64 pageSize = 3;
  int64 Id = 4;
}

message FansItem {
  int64 userId = 1;
  int64 fansUserId = 2;
  int64 followCount = 3;
  int64 fansCount = 4;
  int64 createTime = 5;
}

message FansListResponse {
  repeated FansItem items = 1;
  int64 cursor = 2;
  bool isEnd = 3;
  int64 Id = 4;
}