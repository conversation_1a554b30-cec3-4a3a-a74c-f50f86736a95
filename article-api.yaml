apiVersion: apps/v1
kind: Deployment
metadata:
  name: article-api
  namespace: beyond
  labels:
    app: article-api
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: article-api
  template:
    metadata:
      labels:
        app: article-api
    spec:
      serviceAccountName: endpoints-reader
      containers:
      - name: article-api
        image: article-api:v1.0.0
        ports:
        - containerPort: 80
        readinessProbe:
          tcpSocket:
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 80
          initialDelaySeconds: 15
          periodSeconds: 20
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: timezone
          mountPath: /etc/localtime
      volumes:
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai

---
apiVersion: v1
kind: Service
metadata:
  name: article-api-svc
  namespace: beyond
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: article-api
