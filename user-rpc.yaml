apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-rpc
  namespace: beyond
  labels:
    app: user-rpc
spec:
  replicas: 3
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: user-rpc
  template:
    metadata:
      labels:
        app: user-rpc
    spec:
      containers:
      - name: user-rpc
        image: user-rpc:v1.0.0
        ports:
        - containerPort: 9090
        readinessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 15
          periodSeconds: 20
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: timezone
          mountPath: /etc/localtime
      volumes:
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai