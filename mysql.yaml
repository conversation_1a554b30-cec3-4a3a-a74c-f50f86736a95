apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: beyond
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
      namespace: beyond
    spec:
      containers:
        - name: mysql
          image: mysql:5.7
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: "123456"
          ports:
            - name: mysql
              containerPort: 3306

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: beyond
spec:
  selector:
    app: mysql
  ports:
    - protocol: TCP
      port: 3306
      targetPort: 3306