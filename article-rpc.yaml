apiVersion: apps/v1
kind: Deployment
metadata:
  name: article-rpc
  namespace: beyond
  labels:
    app: article-rpc
spec:
  replicas: 3
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: article-rpc
  template:
    metadata:
      labels:
        app: article-rpc
    spec:
      containers:
      - name: article-rpc
        image: article-rpc:v1.0.0
        ports:
        - containerPort: 9090
        readinessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 9090
          initialDelaySeconds: 15
          periodSeconds: 20
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: timezone
          mountPath: /etc/localtime
      volumes:
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai